import { useState, useEffect } from 'react';
import theme from '../styles/theme';

/**
 * Feedback widget component
 * @param {Object} props - Component props
 * @param {string} props.threadId - Current thread ID
 * @param {Function} props.onSubmitFeedback - Function to handle feedback submission
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {Object} props.feedbackData - Existing feedback data for this thread
 * @returns {JSX.Element|null} - Rendered component or null if no active thread
 */
const FeedbackWidget = ({
  threadId,
  onSubmitFeedback,
  darkMode,
  feedbackData
}) => {
  // State for feedback form
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  // Smiley faces for ratings (1=very sad, 5=very happy)
  const smileys = ['😢', '😞', '😐', '😊', '😄'];

  // State for managing thank you message visibility
  const [showThankYou, setShowThankYou] = useState(true);
  const [fadeOut, setFadeOut] = useState(false);

  // Don't render if there's no thread ID
  if (!threadId) return null;

  // Check if feedback was already submitted for this thread
  const feedbackSubmitted = feedbackData && threadId && feedbackData[threadId];

  // Effect to handle the fade-out and hiding of thank you message
  useEffect(() => {
    if (feedbackSubmitted && showThankYou) {
      // Start fade-out after 2 seconds
      const fadeTimer = setTimeout(() => {
        setFadeOut(true);
      }, 2000);

      // Hide completely after 3 seconds (2s delay + 1s for animation)
      const hideTimer = setTimeout(() => {
        setShowThankYou(false);
      }, 3000);

      // Clean up timers
      return () => {
        clearTimeout(fadeTimer);
        clearTimeout(hideTimer);
      };
    }
  }, [feedbackSubmitted, showThankYou]);

  // Container style
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '8px 0',
    borderTop: darkMode
      ? `1px solid ${theme.colors.dark.border}`
      : `1px solid ${theme.colors.light.border}`,
    backgroundColor: darkMode
      ? theme.colors.dark.surface
      : theme.colors.light.surface,
    marginTop: 'auto'
  };

  // Feedback button style
  const feedbackButtonStyle = {
    background: 'transparent',
    border: 'none',
    color: darkMode ? theme.colors.primary.light : theme.colors.primary.main,
    fontSize: theme.typography.fontSize.sm,
    cursor: 'pointer',
    padding: '4px 12px',
    borderRadius: theme.borderRadius.small,
    fontWeight: theme.typography.fontWeight.medium,
    transition: theme.transitions.fast,
    textDecoration: 'none',
    marginBottom: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px'
  };

  // Smiley container style
  const smileyContainerStyle = {
    display: 'flex',
    justifyContent: 'center',
    gap: '12px',
    marginBottom: '12px'
  };

  // Smiley style
  const getSmileyStyle = (index) => ({
    fontSize: '28px',
    cursor: 'pointer',
    transition: theme.transitions.fast,
    opacity: (hoveredRating || rating) >= index ? 1 : 0.3,
    transform: (hoveredRating || rating) >= index ? 'scale(1.1)' : 'scale(1)',
    filter: (hoveredRating || rating) >= index ? 'none' : 'grayscale(100%)'
  });



  // Submit button style
  const submitButtonStyle = {
    padding: '6px 12px',
    backgroundColor: theme.colors.primary.main,
    color: theme.colors.text.light,
    border: 'none',
    borderRadius: theme.borderRadius.small,
    cursor: isSubmitting ? 'not-allowed' : 'pointer',
    opacity: isSubmitting ? 0.7 : 1,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    transition: theme.transitions.fast
  };

  // Thank you message style
  const thankYouStyle = {
    color: darkMode ? theme.colors.primary.light : theme.colors.primary.main,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    textAlign: 'center',
    padding: '8px 0',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    opacity: fadeOut ? 0 : 1,
    transition: 'opacity 1s ease-out',
    height: fadeOut ? '0' : 'auto',
    overflow: 'hidden',
    marginTop: fadeOut ? '0' : '4px',
    marginBottom: fadeOut ? '0' : '4px'
  };

  // Handle smiley click
  const handleSmileyClick = (selectedRating) => {
    setRating(selectedRating);
  };

  // Handle smiley hover
  const handleSmileyHover = (hoveredValue) => {
    setHoveredRating(hoveredValue);
  };

  // Handle smiley hover leave
  const handleSmileyLeave = () => {
    setHoveredRating(0);
  };

  // Handle feedback submission
  const handleSubmit = async () => {
    if (rating === 0) return;

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmitFeedback(threadId, rating, ''); // No comment, just empty string
      setShowFeedbackForm(false);

      // Reset the fade-out states for the thank you message
      setShowThankYou(true);
      setFadeOut(false);
    } catch (err) {
      setError(err.message || 'Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If feedback was already submitted, show thank you message
  if (feedbackSubmitted) {
    // If showThankYou is false, don't render anything
    if (!showThankYou) {
      return null;
    }

    return (
      <div style={containerStyle}>
        <div style={thankYouStyle}>
          <i className="fas fa-check-circle" style={{ color: '#4CAF50', fontSize: '16px' }}></i>
          Ευχαριστούμε για την αξιολόγησή σας!
        </div>
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      {!showFeedbackForm ? (
        <button
          style={feedbackButtonStyle}
          onClick={() => setShowFeedbackForm(true)}
        >
          <span style={{ fontSize: '16px' }}>😊</span>
          Αξιολογήστε με!
        </button>
      ) : (
        <>
          <div style={smileyContainerStyle} onMouseLeave={handleSmileyLeave}>
            {[1, 2, 3, 4, 5].map((index) => (
              <span
                key={index}
                onClick={() => handleSmileyClick(index)}
                onMouseEnter={() => handleSmileyHover(index)}
                style={getSmileyStyle(index)}
                title={`Rating: ${index}/5`}
              >
                {smileys[index - 1]}
              </span>
            ))}
          </div>

          <button
            onClick={handleSubmit}
            style={submitButtonStyle}
            disabled={isSubmitting || rating === 0}
          >
            {isSubmitting ? 'Υποβολή...' : 'Υποβολή'}
          </button>

          {error && (
            <div style={{ color: 'red', fontSize: theme.typography.fontSize.sm, marginTop: '4px' }}>
              {error}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FeedbackWidget;
